import $ = require("jquery");

import {BaseHistoryItemControl} from "../BaseHistoryItemControl";
import {ServiceRecipientWithEntities} from "ecco-dto";
import {Command, EvidenceDiscriminator, SupportWork} from "ecco-commands";
import {EvidenceHistoryFilter} from "ecco-evidence";

class SupportHistoryItemControl extends BaseHistoryItemControl<SupportWork> {

    /** onlineServiceTypes is provided only when being used online in 'admin mode' */
    constructor(serviceRecipient: ServiceRecipientWithEntities,
                work: SupportWork,
                deleteHandler?: (cmd: Command) => void,
                private attachmentsOnly?: boolean) {
        super(EvidenceDiscriminator.SUPPORT, serviceRecipient, work, deleteHandler);
        this.render();
    }

    protected renderInternal(): void {
        this.element().empty();

        this.renderHeader();

        // line: [client status] / [meeting status] / [location]
        const $statusesDiv = $("<div>");

        const dto = this.work.getDto();
        if (dto.clientStatusId != null) {
            const clientStatusLabel = this.serviceRecipient.features.getMessages()["form.evidenceComment.clientStatus"];
            const listEntryStatus = this.serviceRecipient.features.getListDefinitionEntryById(dto.clientStatusId).getFullName();
            $statusesDiv.append( $("<span>").text("[" + clientStatusLabel + ": " + listEntryStatus + "] "));
            // NB we opt not to have the clientStatusOkay in persistent evidence
            // simply as the current status - we could reconstruct from audits
        }
        if (dto.meetingStatusId != null) {
            const meetingLabel = this.serviceRecipient.features.getMessages()["form.evidenceComment.meetingStatus"];
            let listEntry = this.serviceRecipient.features.getListDefinitionEntryById(dto.meetingStatusId).getFullName();
            $statusesDiv.append( $("<span>").text("[" + meetingLabel + ": " + listEntry + "]"));
        }
        if (dto.locationId != null) {
            const locationLabel = this.serviceRecipient.features.getMessages()["form.evidenceComment.location"];
            const listEntryLocation = this.serviceRecipient.features.getListDefinitionEntryById(dto.locationId).getFullName();
            $statusesDiv.append( $("<span>").text("[" + locationLabel + ": " + listEntryLocation + "]"));
        }
        if ($statusesDiv.children().length > 0) {
            this.element().append($statusesDiv);
        }

        const $suffixSpace = $("<div>");
        this.renderComment($suffixSpace);
        this.element().append($suffixSpace);

        if (dto.riskManagementRequired) {
            if (dto.riskManagementHandled) {
                this.element().append( $("<span>").text("[risk management handled]") );
            } else {
                this.element().append( $("<span>").text("[risk management required]") );
            }
        }

        this.renderAttachments(this.attachmentsOnly);

        // get all actions, sorted by name of outcomes -> actiongroups -> actions
        const outcomes = this.serviceRecipient.configResolver.getOutcomesFilteredForTask(this.work.getDto().taskName);
        const outcomeIdsOrdered = outcomes.map(a => a.getId());
        const sortedActions = this.sortedActions(dto.actions, outcomeIdsOrdered);

        const $actionsList = this.renderActions(sortedActions);

        const $flagsList = this.renderFlags(dto.flags);

        const $assocList = this.associatedActions(dto.associatedActions, dto.actions);

        this.element()
                .append($actionsList)
                .append($flagsList)
                .append($assocList);
    }

    protected override describeAction(actionId: number) {
        return this.serviceRecipient.features.describeSupportAction(actionId);
    }

    public override filterEvidence(filter: EvidenceHistoryFilter): boolean {
        let show = super.filterEvidence(filter);
        if (!show) {
            return false;
        }
        if (!filter.useFilter) {
            return true;
        }
        // if we are showing, then further filter by the target date
        // but if we are not showing, don't apply the filter else we
        // can magically reinstate
        if (show && filter.filterTargets) {
            show = this.work.getDto().actions.some( swa => !!swa.targetDateTime );
        }

        this.element().toggle(show);
        return show;
    }

}

export = SupportHistoryItemControl;
