package com.ecco.dao;

import com.ecco.dom.ClientDetail;
import com.ecco.dom.QReferral;
import com.ecco.dom.Referral;
import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.QTuple;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;

import org.jspecify.annotations.Nullable;
import javax.persistence.QueryHint;
import java.util.List;
import java.util.Set;

import static com.ecco.dom.QReferral.referral;
import static org.hibernate.jpa.QueryHints.HINT_READONLY;

public interface ReferralRepository extends QueryDslPredicateAndProjectionExecutor<Referral, Long>,
        ReferralRepositoryCustom {
    OrderSpecifier<Long> SORT_BY_CLIENT_ID = new OrderSpecifier<>(Order.ASC, referral.client.id);

    QReferral parent = new QReferral("parentReferral");

    QTuple PARENT_CHILD_SRID_PROJECTION =
            Projections.tuple(parent.id, referral.serviceRecipient.id);

    QClientDetailWrapper CLIENT_PROJECTION = new QClientDetailWrapper(referral.client);

    QReferralSummary REFERRAL_SUMMARY_PROJECTION =
            new QReferralSummary(
                referral.requestedDelete,
                referral.id, referral.code, referral.serviceRecipient.id,
                referral.parentReferral.id,
                referral.primaryReferral.id,
                referral.relationshipToPrimaryReferralId,
                referral.client.id, referral.client.code,
                referral.serviceRecipient.serviceAllocationId,
                referral.serviceRecipient.serviceTypeId,
                referral.serviceRecipient.serviceAllocation.service.id,
                referral.serviceRecipient.serviceAllocation.project.id,
                referral.client.contact.firstName, referral.client.contact.lastName,
                referral.client.contact.id, referral.client.contact.calendarId,
                referral.pendingStatus.id, referral.firstResponseMadeOn,
                referral.firstOfferedInterviewDate,
                referral.interviewDna,
                referral.interviewDnaComments,
                referral.interviewSetupComments,
                referral.interviewer1.id,
                referral.interviewer2.id,
                referral.interviewLocation,
                referral.decisionReferralMadeOnDT,
                referral.interviewDate,
                referral.decisionMadeOnDT,
                referral.receivedDate, referral.receivingServiceDate,
                referral.exited,
                referral.selfReferral, referral.agency.id, referral.referrer.id,
                referral.referralReason,
                referral.srcGeographicArea.id,
                referral.signpostedComment.id,
                referral.finalDecision,
                referral.acceptedOnService, referral.acceptedReferral,
                referral.serviceRecipient.dataProtectionSignatureId,
                referral.dataProtectionSigned,
                referral.dataProtectionStatus,
                referral.serviceRecipient.consentSignatureId,
                referral.consentSigned,
                referral.consentStatus,
                referral.serviceRecipient.agreementSignatureId,
                referral.agreementSigned,
                referral.agreementStatus,
                referral.serviceRecipient.agreement2SignatureId,
                referral.agreement2Signed,
                referral.agreement2Status,
                referral.serviceRecipient.agreement3SignatureId,
                referral.agreement3Signed,
                referral.agreement3Status,
                referral.serviceRecipient.agreement4SignatureId,
                referral.agreement4Signed,
                referral.agreement4Status,
                referral.serviceRecipient.agreement5SignatureId,
                referral.agreement5Signed,
                referral.agreement5Status,
                referral.serviceRecipient.agreement6SignatureId,
                referral.agreement6Signed,
                referral.agreement6Status,
                referral.serviceRecipient.agreement7SignatureId,
                referral.agreement7Signed,
                referral.agreement7Status,
                referral.serviceRecipient.agreement8SignatureId,
                referral.agreement8Signed,
                referral.agreement8Status,
                referral.serviceRecipient.agreement9SignatureId,
                referral.agreement9Signed,
                referral.agreement9Status,
                referral.serviceRecipient.agreement10SignatureId,
                referral.agreement10Signed,
                referral.agreement10Status,
                referral.signpostReason.id,
                referral.signpostedBack,
                referral.signpostedAgency.id,
                referral.supportWorker.id, referral.exitReason.id,
                referral.meetingDays,
                referral.serviceRecipient.nextSlaDueDate,
                referral.serviceRecipient.nextDueSlaTaskId,
                referral.serviceRecipient.latestClientStatusId,
                referral.serviceRecipient.latestClientStatusDateTime,
                referral.serviceRecipient.latestClientStatusOkayDateTime,
                referral.acceptedFunding,
                referral.fundingSource.id,
                referral.fundingHoursOfSupport,
                referral.decisionFundingDate,
                referral.waitingListScore,
                referral.serviceRecipient.currentTaskIndex,
                referral.serviceRecipient.currentTaskId,
                referral.client.contact
            );

    @Query("SELECT r FROM Referral r WHERE id = ?1")
    @EntityGraph(attributePaths={"choicesMap", "textMap", "dateMap"})
    Referral findOneForSimpleUpdate(long id);

    @Query("SELECT r FROM Referral r WHERE id in (?1)")
    @EntityGraph(attributePaths={"choicesMap", "textMap", "dateMap"})
    List<Referral> findAllCustomFields(List<Long> id);

    @Query("SELECT id FROM Referral order by id asc")
    List<Long> findAllIds();

    @Query("SELECT r.id FROM Referral r where r.serviceRecipient.serviceAllocation.service.id in (?1) order by id asc")
    List<Long> findAllIdsInServices(List<Long> ids);

    /** Utility to help us when we're using the evidence domain which is based on service recipient */
    @Query("SELECT r.serviceRecipient.id from Referral r where id = ?1")
    int getServiceRecipientId(long referralId);

    /** Utility to help us when we're using the evidence domain which is based on service recipient */
    @Query("SELECT r.serviceRecipient.id from Referral r where r.client.id = ?1")
    Set<Integer> findAllServiceRecipientIdsFromClientId(long clientId);

    /** Utility to help us when we're using the evidence domain which is based on service recipient
     * @return referralId of matching referral or null if the id does not relate to a referral
     */
    @Query("SELECT r.id from Referral r where r.serviceRecipient.id = ?1")
    @Nullable
    Long getReferralIdByServiceRecipientId(int serviceRecipientId);

    /** Utility to help us.
     * @return clientId of matching srId or null if the id does not relate to a referral
     */
    @Query("SELECT r.client.id from Referral r where r.serviceRecipient.id = ?1")
    @Nullable
    Long getClientIdByServiceRecipientId(int serviceRecipientId);

    List<Referral> findByClient(ClientDetail client);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<Referral> findByClientId(long clientId);



    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<Referral> findAllByCode(String code);

    /** Find referrals for client resident in the given building */
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<Referral> findAllByClient_ResidenceIdIn(List<Integer> buildingIds);

    /** Find calendarIds for clients in a buidling */
    @Query("SELECT DISTINCT r.client.contact.calendarId from Referral r where r.client.residenceId = ?1")
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<String> findDistinctCalendarIdsByClient_Residence_Id(int buildingId);

    /** Find the children of this referralId */
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<Referral> findAllByParentReferral_Id(long parentReferralId);

    /** Find the children of this referralId */
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<Referral> findAllByPrimaryReferral_Id(long parentReferralId);

    /*
     * SQL: "select r.id from referrals r"
            + " join contacts c on r.supportworkerid = c.id"
            + " join users u on c.usersid = u.id"
            + " join group_members gm on gm.username = u.username"
            + " join groups g on gm.group_id = g.id"
            + " where g.group_name = ?1")
     */
    @Query("select r from Referral r, Individual c, GroupMember gm"
            + " join c.user u"
            + " where"
            + " r.supportWorker = c AND"
            + " gm.member.id = u.id AND"
            + " gm.group.name = ?1"
            )
    List<Referral> findAllReferralsForSupportWorkerInGroup(String groupName);

// This nice try generated incorrect SQL
//    @Modifying
//    @Query("UPDATE Referral r"
//    + " SET r.meetingDays = ?2"
//    + " WHERE id = ?1")
//    void updateMeetingDays(long referralId, DaysOfWeek days);

    @Modifying
    @Query(nativeQuery=true, value = "UPDATE referrals"
    + " SET version=version + 1, sunday = ?2, monday = ?3, tuesday = ?4, wednesday = ?5, thursday = ?6, friday = ?7, saturday = ?8"
    + " WHERE serviceRecipientId = ?1")
    void updateMeetingDays(long serviceRecipientId,
            boolean sun, boolean mon, boolean tue, boolean wed, boolean thur, boolean fri, boolean sat);

    Referral findOneByIdAndRequestedDeleteIsNotNull(Long referralId);

    Referral findByServiceRecipient_Id(Integer serviceRecipientId);

    @Modifying
    @Query(nativeQuery=true, value = "UPDATE referrals"
            + " SET version=version + 1, srcGeographicArea = ?2"
            + " WHERE id = ?1")
    void updateArea(long id, int srcGeographicAreaId);

    @Modifying
    @Query("UPDATE Referral SET version=version + 1, client.id = :cId WHERE serviceRecipient.id = :srId")
    void moveReferralToClient(@Param("srId") int srId, @Param("cId") long cId);

    @Modifying
    @Query("UPDATE Referral SET agency.id = :newCompanyId WHERE agency.id = :oldCompanyId")
    void bulkSwitchAgency(
            @Param("oldCompanyId") long oldCompanyId,
            @Param("newCompanyId") long newCompanyId);

    @Modifying
    @Query("UPDATE Referral SET signpostedAgency.id = :newCompanyId WHERE signpostedAgency.id = :oldCompanyId")
    void bulkSwitchSignpostedAgency(
            @Param("oldCompanyId") long oldCompanyId,
            @Param("newCompanyId") long newCompanyId);
}
