import {ReportCriteriaDto} from "ecco-dto";
import * as types from "@eccosolutions/ecco-common";

/**
 * Referral statuses presented in the selection criteria of reporting. This is the list to present,
 * but defined reports can refer to all the available statuses from ReferralStatusName.java.
 */
export var asAtText = "(as at date)";
export var statuses = [
    //{id: "allNoDates", name: "all"}, // see ReferralStatusName.java and ReferralListFilter for what these do
    //{id: "created", name: "created"},
    {id: "received", name: "received (in the period)"},
    //{id: "ongoing", name: "ongoing"},
    {id: "acceptedService", name: "accepted (in the period)"},
    {id: "liveAtEnd", name: "open ".concat(asAtText)}, // from reporting, make live be 'live at the end of the period'
    {id: "live", name: "open (some point during the period)"},
    {id: "movein", name: "open (in the period)"},
    {id: "moveinAtEnd", name: "open ".concat(asAtText)},
    {id: "incompleteAtEnd", name: "new ".concat(asAtText)},
    {id: "waitingAtEnd", name: "waiting ".concat(asAtText)},
    {id: "signposted", name: "signposted (in the period)"},
    {id: "exited", name: "exited (in the period)"}
];

export var statusesById: types.StringToStringMap = {};
statuses.forEach(status => {
    statusesById[status.id] = status.name;
});

export function statusIsAtEnd(status: string | undefined) {
    return status && status.indexOf("AtEnd") > -1;
}

export enum ReferralStatus {
    AllNoDates = 1,
    Created = 2,
    Received = 3,
    Ongoing = 4,
    AcceptedService = 5,
    LiveAtEnd = 6,
    Live = 7,
    MoveIn = 8,
    MoveInAtEnd = 19,
    Incomplete = 9,
    IncompleteAtEnd = 10,
    Waiting = 11,
    WaitingAtEnd = 12,
    Signposted = 13,
    SignpostedReferral = 14,
    SignpostedService = 15,
    Exited = 16
    // ClosedUnsuccessful = 17,
    // ClosedSuccessful = 18
}

export var ReferralStatusLookupById: types.SparseArray<string> = {};
ReferralStatusLookupById[ReferralStatus.AllNoDates] = "allNoDates";
ReferralStatusLookupById[ReferralStatus.Created] = "created";
ReferralStatusLookupById[ReferralStatus.Received] = "received";
ReferralStatusLookupById[ReferralStatus.Ongoing] = "ongoing";
ReferralStatusLookupById[ReferralStatus.AcceptedService] = "acceptedService";
ReferralStatusLookupById[ReferralStatus.LiveAtEnd] = "liveAtEnd";
ReferralStatusLookupById[ReferralStatus.Live] = "live";
ReferralStatusLookupById[ReferralStatus.MoveIn] = "movein";
ReferralStatusLookupById[ReferralStatus.MoveInAtEnd] = "moveinAtEnd";
ReferralStatusLookupById[ReferralStatus.Incomplete] = "incomplete";
ReferralStatusLookupById[ReferralStatus.IncompleteAtEnd] = "incompleteAtEnd";
ReferralStatusLookupById[ReferralStatus.Waiting] = "waiting";
ReferralStatusLookupById[ReferralStatus.WaitingAtEnd] = "waitingAtEnd";
ReferralStatusLookupById[ReferralStatus.Signposted] = "signposted";
ReferralStatusLookupById[ReferralStatus.SignpostedReferral] = "signposted-referral";
ReferralStatusLookupById[ReferralStatus.SignpostedService] = "signposted-service";
ReferralStatusLookupById[ReferralStatus.Exited] = "exited";
// ReferralStatusLookupById[ReferralStatus.ClosedUnsuccessful] = "closed-unsuccessful";
// ReferralStatusLookupById[ReferralStatus.ClosedSuccessful] = "closed-successful";

export class ReferralStatusHandler {
    public static referralStatusAtEndDate(referralStatus: string | undefined | null) {
        let applicableStatusAtEndDate = [
            ReferralStatus.LiveAtEnd,
            ReferralStatus.IncompleteAtEnd,
            ReferralStatus.WaitingAtEnd,
            ReferralStatus.MoveInAtEnd
        ];
        return referralStatus
            ? applicableStatusAtEndDate.some(
                  status => ReferralStatusLookupById[status] == referralStatus
              )
            : false;
    }

    public static referralStatusReceivedDuringPeriodAllowed(
        referralStatus: string | undefined | null
    ) {
        let applicableReferralStatus = [
            ReferralStatus.Ongoing,
            ReferralStatus.MoveIn,
            ReferralStatus.Live,
            ReferralStatus.Waiting,
            ReferralStatus.Incomplete
        ];
        return referralStatus
            ? applicableReferralStatus.some(
                  status => ReferralStatusLookupById[status] == referralStatus
              )
            : false;
    }

    public static getReferralStatusPath(
        reportCriteria: ReportCriteriaDto,
        isReferralReport: boolean
    ): string {
        let reportPathText = "";
        if (reportCriteria.referralStatus) {
            let referralStatusText = statusesById[reportCriteria.referralStatus];

            // if we are not referral based - then we keep the from/to dates and change the status text to be 'at end date'
            if (!isReferralReport) {
                reportPathText = reportPathText.concat(" and referrals ");
                if (ReferralStatusHandler.referralStatusAtEndDate(reportCriteria.referralStatus)) {
                    referralStatusText = referralStatusText.replace(asAtText, "(at end date)");
                }
            }
            reportPathText = reportPathText.concat(referralStatusText);

            // only show 'limited to received' if we are not received, otherwise this is unnecessary and confusing for the user
            if (
                reportCriteria.newReferralsOnly &&
                ReferralStatusLookupById[ReferralStatus.Received] != reportCriteria.referralStatus
            ) {
                reportPathText = reportPathText.concat(" limited to received date (in the period)");
            }
        }

        return reportPathText;
    }
}
