<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
    logicalFilePath="2024/general-domain">

    <changeSet id="DEV-2709-st-referralaspects-bldg" author="neale" context="1.1-base-data">
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="40"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="18"/>  <!-- referralView -->
            <column name="servicetypeId" valueNumeric="-100"/>
        </insert>
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="50"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="50"/>  <!-- close (e.g. sold or end of lease) -->
            <column name="servicetypeId" valueNumeric="-100"/>
        </insert>
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="11"/>  <!-- endFlow -->
            <column name="servicetypeId" valueNumeric="-100"/>
        </insert>
    </changeSet>

    <changeSet id="20240601-1-add-charge-category-to-fixed-container" author="copilot">
        <comment>Add chargeCategoryId column to bldg_fixed table to support service charge categories</comment>
        <addColumn tableName="bldg_fixed">
            <column name="chargeCategoryId" type="int">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addForeignKeyConstraint
            baseTableName="bldg_fixed"
            baseColumnNames="chargeCategoryId"
            constraintName="fk_bldg_fixed_charge_category"
            referencedTableName="cfg_list_definitions"
            referencedColumnNames="id"
            />
    </changeSet>

    <!-- remove the chargeCategoryId, we need to match multiple charge categories to a building -->
    <changeSet id="20240601-1-remove-charge-category-to-fixed-container" author="copilot">
        <comment>Remove chargeCategoryId column from bldg_fixed table</comment>
        <dropForeignKeyConstraint baseTableName="bldg_fixed" constraintName="fk_bldg_fixed_charge_category"/>
        <dropColumn tableName="bldg_fixed" columnName="chargeCategoryId"/>
    </changeSet>

    <changeSet id="DEV-2725-add-charge-category-combinations-to-fixed-container" author="adamjhamer">
        <comment>Add chargeCategoryCombinations JSON column to bldg_fixed table to support multiple charge category and name combinations</comment>
        <addColumn tableName="bldg_fixed">
            <column name="chargeCategoryCombinations" type="CLOB">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- create, but we can't pre-populate easily without specific config -->
    <changeSet id="DEV-2726-add-sr-okayDateTime" author="adamjhamer">
        <addColumn tableName="servicerecipients">
            <column name="latestClientStatusOkayDateTime" type="DATETIME">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
