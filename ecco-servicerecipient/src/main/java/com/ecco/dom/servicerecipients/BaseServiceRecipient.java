package com.ecco.dom.servicerecipients;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dom.Contactable;
import com.ecco.dom.Individual;
import com.ecco.dom.contacts.AddressLike;
import com.ecco.infrastructure.Created;
import com.ecco.infrastructure.entity.AbstractUnidentifiedVersionedEntity;
import com.ecco.dom.IntegerKeyedEntity;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.ecco.servicerecipient.ServiceRecipientMessages;
import com.querydsl.core.annotations.QueryInit;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * This relates to Referral or Building or Worker (HR) record which has the attributes of being able to receive a
 * service including standard concepts such as an associated workflow and service agreements.
 */
@DiscriminatorColumn(name = "discriminator_orm", discriminatorType = DiscriminatorType.STRING)
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@Table(name = "servicerecipients")
@Entity
@Getter
@Setter
public abstract class BaseServiceRecipient extends AbstractUnidentifiedVersionedEntity<Integer>
    implements Created, IntegerKeyedEntity, Contactable, ServiceRecipientMessages {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(generator="svc_recipientTableGenerator")
    @TableGenerator(
            name = "svc_recipientTableGenerator", initialValue = 100000, pkColumnValue = "svc_recipient",
            allocationSize = 1, table = "hibernate_sequences")
    @Column(name="id", nullable=false) // oracle doesn't like using unique=true
    private Integer id = null;

    @Column(name="discriminator_orm", insertable = false, updatable = false)
    @Setter(AccessLevel.NONE) // no public setter method - don't give the impression we can set this
    private String discriminator;

    @SuppressWarnings({"JpaModelReferenceInspection", "unused"})
    @OneToMany(mappedBy = "serviceRecipientId", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<ServiceRecipientCommand> commands;

    @Column(name="consentSignatureId", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID consentSignatureId;

    @Column(name="agreementSignatureId", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID agreementSignatureId;

    @Column(name="agreement2SignatureId", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID agreement2SignatureId;

    @Column(name="agreement3SignatureId", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID agreement3SignatureId;

    @Column(name="agreement4SignatureId", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID agreement4SignatureId;

    @Column(name="agreement5SignatureId", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID agreement5SignatureId;

    @Column(name="agreement6SignatureId", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID agreement6SignatureId;

    @Column(name="agreement7SignatureId", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID agreement7SignatureId;

    @Column(name="agreement8SignatureId", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID agreement8SignatureId;

    @Column(name="agreement9SignatureId", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID agreement9SignatureId;

    @Column(name="agreement10SignatureId", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID agreement10SignatureId;

    @Column(name="dataProtectionSignatureId", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID dataProtectionSignatureId;

    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime created;

    /**
     * the taskDefinition.id of the current task if LinearWorkflow is in use
     */
    @Nullable
    @Column(name="currentTaskId")
    private Long currentTaskId;

    @Column(name="nextDueSlaTaskId")
    private Long nextDueSlaTaskId;

    @Column
    private LocalDate nextSlaDueDate;

    /**
     * The count of how many taskdefinitions have been completed on its servicetype.
     * Its used currently in referralView_tasks.jspf for aspectDone or aspectNow
     * <pre>select * from servicetypes_taskdefinitions stra, taskdefinitions ra
     *  where servicetypeId ={serviceTypeId} and stra.taskdefinitionId = ra.id
     *  order by orderby;</pre>
     */
    @Column
    private int currentTaskIndex;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "serviceAllocationId")
    @QueryInit("*.*")
    protected ServiceCategorisation serviceAllocation;

    @Column(name="serviceAllocationId", insertable = false, updatable = false)
    protected int serviceAllocationId;

    private Long serviceTypeId;

    /** @see CommentCommandViewModel#clientStatusId */
    @SuppressWarnings("unused") // This is set via ServiceRecipientRepository
    @Column
    private Integer latestClientStatusId;

    @Column
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime latestClientStatusDateTime;

    @Column
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime latestClientStatusOkayDateTime;

    @ManyToMany(fetch=FetchType.LAZY)
    @JoinTable(name = "svcrec_provides_attrs",
            joinColumns = @JoinColumn(name = "serviceRecipientId"),
            inverseJoinColumns = @JoinColumn(name = "attributeId"))
    private Set<ListDefinitionEntry> providesAttributes;

    @ManyToMany(fetch=FetchType.LAZY)
    @JoinTable(name = "svcrec_requires_attrs",
            joinColumns = @JoinColumn(name = "serviceRecipientId"),
            inverseJoinColumns = @JoinColumn(name = "attributeId"))
    private Set<ListDefinitionEntry> requiresAttributes;

    public Long getPermissionServiceId() {
        // try to move away from getServiceAllocation()...
        // return serviceRepository.findOneByServiceCategorisationId(serviceAllocationId);
        return serviceAllocation.getServiceId();
    }

    public Long getPermissionProjectId() {
        // try to move away from getServiceAllocation()...
        // return serviceRepository.findOneByServiceCategorisationId(serviceAllocationId);
        return serviceAllocation.getProjectId();
    }

    public String getParamsOfService(String param) {
        // try to move away from getServiceAllocation()...
        // return serviceRepository.findOneByServiceCategorisationId(serviceAllocationId);
        return serviceAllocation.getService().getParameterAsString(param);
    }

    public int loadConfigServiceTypeId() {
        // try to move away from getServiceAllocation()...
        // return serviceCategorisationRepository.findServiceTypeId(serviceAllocationId);
        if (serviceTypeId != null) {
            return serviceTypeId.intValue();
        } else {
            return serviceAllocation.loadServiceTypeId();
        }
    }


    /** Get a prefix representing what type of service recipient this is:
     *      (r)eferral, (b)uilding, (w)orker, (i)ncident, (m)maint/repair, (mv)managed void, (ct)ontract (gs)roupsupport */
    @NonNull
    public abstract String getPrefix();

    public abstract String getDisplayName();

    public abstract String getCalendarId();

    /** Return the contact related to this service recipient if it has one (i.e. for Referral and Worker) */
    public abstract @Nullable Individual getContact();

    public abstract Map<String,String> getTextMap();

    public abstract AddressLike getAddress();

    /** e.g. return Referral.getCode() or whatever */
    public abstract String getParentCode();

    /** e.g. return Worker/Referral.getId() or whatever */
    public abstract Long getParentId();


    public void setCurrentTaskIndex(int currentTaskIndex) {
        // only set if greater - could be that we are 'going back' - eg safeguarding...
        if (currentTaskIndex > this.currentTaskIndex) {
            this.currentTaskIndex = currentTaskIndex;
        }
    }

}
