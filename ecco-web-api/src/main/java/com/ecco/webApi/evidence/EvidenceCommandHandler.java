package com.ecco.webApi.evidence;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.*;
import com.ecco.dom.commands.CommentCommand;
import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.dom.servicerecipients.QServiceRecipientCommand;
import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import com.ecco.evidence.EvidenceTask;
import com.ecco.evidence.ParentChildId;
import com.ecco.evidence.ParentChildResolver;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.service.TaskDefinitionService;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.webApi.CommandResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ecco.calendar.core.CalendarService;
import com.ecco.calendar.core.Recurrence;
import com.ecco.calendar.core.util.DateTimeUtils;
import org.jspecify.annotations.NonNull;
import org.joda.time.DateTimeZone;
import org.joda.time.Instant;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import org.jspecify.annotations.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.Serializable;
import java.net.URI;
import java.util.UUID;

/**
 * Handler for other handlers to know whether to save work on a parent recipient for central processing (deprecated).
 * Saving on a parent was triggered through the flag on serviceType.isChildService(). This was used in
 * conjunction with service-config-domain.ts ConfigResolverDefault which determines what to show on child recipients.
 */
public abstract class EvidenceCommandHandler<VM extends BaseServiceRecipientCommandViewModel,
            ENTITY extends ServiceRecipientCommand,
            PARAMS extends EvidenceParams>
        extends ServiceRecipientCommandHandler<VM, ENTITY, PARAMS>
        implements ParentChildHandler<VM, ENTITY, PARAMS>,
                   DraftableCommandHandler<VM, Long, ENTITY, PARAMS> {

    public static String COMMENTTYPE_PREFIX = "migrate:commenttypes-";

    @PersistenceContext
    protected EntityManager entityManager;

    @NonNull
    protected final ServiceRecipientRepository serviceRecipientRepository;
    @NonNull
    protected final ServiceRepository serviceRepository;
    @NonNull
    protected final ParentChildResolver parentChildResolver;
    @NonNull
    protected final CalendarService calendarService;
    @NonNull
    protected final EntityUriMapper entityUriMapper;
    @NonNull
    protected final TaskDefinitionService taskDefinitionService;

    public EvidenceCommandHandler(@NonNull ObjectMapper objectMapper,
                                  @NonNull ServiceRecipientRepository serviceRecipientRepository,
                                  @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                  @NonNull ServiceRepository serviceRepository,
                                  @NonNull ParentChildResolver parentChildResolver,
                                  @NonNull CalendarService calendarService,
                                  @NonNull TaskDefinitionService taskDefinitionService,
                                  @NonNull EntityUriMapper entityUriMapper,
                                  @NonNull Class<VM> vmClass) {
        super(objectMapper, serviceRecipientCommandRepository, vmClass);
        this.serviceRecipientRepository = serviceRecipientRepository;
        this.serviceRepository = serviceRepository;
        this.parentChildResolver = parentChildResolver;
        this.calendarService = calendarService;
        this.entityUriMapper = entityUriMapper;
        this.taskDefinitionService = taskDefinitionService;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, PARAMS params, @NonNull VM viewModel) {
        BaseServiceRecipientEvidence recipient = serviceRecipientRepository.findEvidenceCapableById(params.serviceRecipientId).orElse(null);

        var type = taskDefinitionService.getTaskType(EvidenceTask.fromTaskName(params.taskName));
        if (taskDefinitionService.isAuditBased(type)) {
            log.warn("Only storing command for srid={}", params.serviceRecipientId);
            return null;
        }

        ParentChildId id = parentChildResolver.getParentChildId(recipient);

        return handleInternal(id.getParentServiceRecipientId(), id.getChildServiceRecipientId(), auth, params, viewModel);
    }

    protected BaseServiceRecipient getParentAsRef(int parentServiceRecipientId) {
        return entityManager.getReference(BaseServiceRecipient.class, parentServiceRecipientId);
    }

    protected BaseServiceRecipient getChildAsRef(@Nullable Integer childServiceRecipientId) {
        return childServiceRecipientId == null ? null
                : entityManager.getReference(BaseServiceRecipient.class, childServiceRecipientId);
    }

    /**
     * Ensure we have a concrete entry before we save the event.
     */
    public boolean ensureConcreteRecurrence(URI uri, String calEventId) {
        if (calendarService.isRecurrence(calEventId)) {
            calendarService.ensureConcreteRecurrence(Recurrence.RecurrenceHandle.fromString(calEventId), uri);
            // NB this is what calendarService (cosmo) does - we're just making it visible
            entityManager.flush();
            return true;
        }
        return false;
    }

    /** For new work, we use the timestamp for the workDate as we otherwise don't know.
     *  A later CommentCommand may then update it with a real work date.
     *  NB This is called from a few places which then go on to handle additional data - which could be consolidated
     *  into further methods in this class - eg ensureConcreteRecurrence.
     */
    public SupportEvidenceBuilder createNewSupportWork(
            int parentServiceRecipientId,
            @Nullable Integer childServiceRecipientId,
            // current user recording the work
            Authentication auth,
            EvidenceTask task, EvidenceGroup evidenceGroup, UUID workUuid, Instant timestamp,
            @Nullable ClientDetail client) {

        SupportEvidenceBuilder builder = new SupportEvidenceBuilder(parentServiceRecipientId);
        builder
                .setChild(getChildAsRef(childServiceRecipientId))
                .fromSource(task, evidenceGroup)
                .setId(workUuid)
                .withWorkDate(timestamp)
                .withCreatedDate(timestamp.toDateTime(DateTimeZone.UTC))
                .withAuthor(auth)
                .withClient(client);
        return builder;
    }

    /** For new work, we use the timestamp for the workDate as we otherwise don't know.
     *  A later CommentCommand may then update it with a real work date.
     */
    protected RiskEvidenceBuilder createNewRiskWork(int parentServiceRecipientId, Integer childServiceRecipientId, Authentication auth,
                                                          EvidenceTask task, EvidenceGroup evidenceGroup, UUID workUuid, Instant timestamp,
                                                          @Nullable ClientDetail client) {

        RiskEvidenceBuilder builder = new RiskEvidenceBuilder(parentServiceRecipientId);
        builder
                .setChild(getChildAsRef(childServiceRecipientId))
                .fromSource(task, evidenceGroup)
                .setId(workUuid)
                .withWorkDate(timestamp)
                .withCreatedDate(timestamp.toDateTime(DateTimeZone.UTC))
                .withAuthor(auth)
                .withClient(client);
        return builder;
    }

    /** For new work, we use the timestamp for the workDate as we otherwise don't know.
     *  A later CommentCommand may then update it with a real work date.
     */
    protected EvidenceFormWork.Builder createNewFromWork(int parentServiceRecipientId, Integer childServiceRecipientId, Authentication auth,
                                                         EvidenceTask task, EvidenceGroup evidenceGroup, UUID workUuid, Instant timestamp,
                                                         @Nullable ClientDetail client) {

        EvidenceFormWork.Builder builder = EvidenceFormWork.builder(parentServiceRecipientId);
        builder
                .setChild(getChildAsRef(childServiceRecipientId))
                .fromSource(task, evidenceGroup)
                .setId(workUuid)
                .withWorkDate(timestamp)
                .withCreatedDate(timestamp.toDateTime(DateTimeZone.UTC))
                .withAuthor(auth)
                .withClient(client);
        return builder;
    }

    protected void applyViewModel(CommentCommandViewModel viewModel, SupportEvidenceBuilder builder) {
        this.applyCommentViewModel(viewModel, builder);

        if (viewModel.eventId != null) {
            builder.withEventId(viewModel.eventId.to);
        }
        if (viewModel.minsTravel != null) {
            builder.withMinutesTravel(viewModel.minsTravel.to);
        }
        if (viewModel.mileageTo != null) {
            builder.withMileageTo(viewModel.mileageTo.to);
        }
        if (viewModel.mileageDuring != null) {
            builder.withMileageDuring(viewModel.mileageDuring.to);
        }
        if (viewModel.riskManagementRequired != null) {
            builder.riskManagementRequired(viewModel.riskManagementRequired.to);
        }
        if (viewModel.clientStatusId != null) {
            builder.withClientStatus(viewModel.clientStatusId.to);
            // additionally, update the service recipient
            if (viewModel.clientStatusId.to != null) {
                var date = viewModel.workDate != null
                        ? DateTimeUtils.convertFromUsersLocalDateTime(viewModel.workDate.to)
                        : DateTimeUtils.getUtcNow();
                if (Boolean.TRUE.equals(viewModel.clientStatusOkay)) {
                    serviceRecipientRepository.updateLatestClientStatusOkay(viewModel.serviceRecipientId,
                            viewModel.clientStatusId.to, date, date);
                } else {
                    serviceRecipientRepository.updateLatestClientStatus(viewModel.serviceRecipientId,
                            viewModel.clientStatusId.to, date);
                }
            }
        }
        if (viewModel.meetingStatusId != null) {
            builder.withMeetingStatus(viewModel.meetingStatusId.to);
        }
        if (viewModel.locationId != null) {
            builder.withLocation(viewModel.locationId.to);
        }
    }

    protected void applyCommentViewModel(CommentCommandViewModel viewModel, EvidenceWithCommentBuilder builder) {
        if (viewModel.workDate != null) {
            builder.withWorkDate(viewModel.workDate.to);
        }
        if (viewModel.comment != null) {
            builder.withComment(viewModel.comment.to);
        }
        if (viewModel.minsSpent != null) {
            builder.withMinutesSpent(viewModel.minsSpent.to);
        }
        if (viewModel.commentTypeId != null) {
            builder.withType(viewModel.commentTypeId.to);
        }
    }

    protected CommentCommand createCommand(Serializable targetId, EvidenceParams params, String requestBody, CommentCommandViewModel viewModel, long userId) {
        Assert.state(params.serviceRecipientId == viewModel.serviceRecipientId, "serviceRecipientId in body must match URI");

        return new CommentCommand(
                viewModel.uuid,
                viewModel.timestamp,
                userId,
                requestBody,
                params.serviceRecipientId,
                params.evidenceGroupKey,
                params.taskName);
    }

    @Override
    public void clearDrafts(Authentication authentication, PARAMS goalParams, String requestBody, VM viewModel, ENTITY command, long userId) {
        // NB could use this.commandRepository if we had more generics in all the handlers - when we have more draft handlers, perhaps
        // index "DEV-2694-svcrec_commands-index-draft"
        QServiceRecipientCommand cmd = QServiceRecipientCommand.serviceRecipientCommand;
        var thisUsersDrafts = commandRepository.findAll(cmd.draft.isTrue().and(cmd.userId.eq(userId).and(cmd.serviceRecipientId.eq(goalParams.serviceRecipientId))));
        commandRepository.deleteAllInBatch(thisUsersDrafts);
    }

}
