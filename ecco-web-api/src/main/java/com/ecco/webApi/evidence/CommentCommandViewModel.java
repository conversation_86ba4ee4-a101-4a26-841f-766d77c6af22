package com.ecco.webApi.evidence;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.EvidenceGroup;

import com.ecco.dto.AddedRemovedDto;
import com.ecco.dto.ChangeViewModel;
import com.ecco.dto.FlagsDefinitionCommandDto;
import com.ecco.evidence.EvidenceTask;
import org.joda.time.LocalDateTime;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

public class CommentCommandViewModel extends BaseServiceRecipientCommandViewModel {

    /**
     * Indicates whether this command originated from an external source
     */
    @Nullable
    public Boolean externalSource;

    /**
     * workUuid
     */
    @NonNull
    public UUID workUuid;

    public String operation;

    // we supply this client-side, so lets use it to be able to send emails
    public String taskName;

    public LocationViewModel location;

    /**
     * Optional calendar event id (format = uuid[:iso8601start-date-time])
     */
    @Nullable
    public ChangeViewModel<String> eventId;

    /**
     * Optional calendar event outcome for rota visits
     */
    @Nullable
    public ChangeViewModel<Integer> eventStatusRateId;

    /**
     * The comment entered when this Support Work was recorded.
     */
    @Nullable
    public ChangeViewModel<String> comment;

    /**
     * The date the work took place (e.g. when person was visited)
     */
    @Nullable
    public ChangeViewModel<LocalDateTime> workDate;

    /**
     * The id for the type of work (e.g. meeting, phone call)
     */
    @Nullable
    public ChangeViewModel<Integer> commentTypeId;

    /**
     * Number of minutes spent on the support work
     */
    @Nullable
    public ChangeViewModel<Integer> minsSpent;

    /**
     * Number of minutes spent travelling to/from the recipient
     */
    @Nullable
    public ChangeViewModel<Integer> minsTravel;

    /**
     * Mileage to the visit (rota)
     */
    @Nullable
    public ChangeViewModel<BigDecimal> mileageTo;

    /**
     * Mileage during the visit with the client (rota)
     */
    @Nullable
    public ChangeViewModel<BigDecimal> mileageDuring;

    /**
     * If the support item requires risk management
     */
    @Nullable
    public ChangeViewModel<Boolean> riskManagementRequired;

    /**
     * The id for the review in progress
     */
    public Long reviewId;

    /**
     * Ids of the files to add
     */
    public Long[] attachmentIdsToAdd;

    /**
     * Generalised flags based on list-def entry.
     */
    public AddedRemovedDto<Integer> flagIds;

    /**
     * The UUIDs of the support work with 'requires risk management' which is being dealt with in this piece of work
     */
    @Nullable
    public ChangeViewModel<List<UUID>> riskManagementHandled;

    /**
     * The id for the clientStatus list def
     * clientStatus is typically 'whereabouts unknown'/ 'located and OK'
     */
    @Nullable
    public ChangeViewModel<Integer> clientStatusId;

    /**
     * When a clientStatusId is provided, indicate whether this is an 'okay' status
     */
    @Nullable
    public Boolean clientStatusOkay;

    /**
     * The id for the meetingStatus list def
     * meetingStatus is typically 'client cancelled' / 'did not attend' etc.
     * This is saved on evidence history.
     */
    @Nullable
    public ChangeViewModel<Integer> meetingStatusId;

    /** As per meetingStatusId, it's the result of the event
     * but this is saved on the event not evidence history */
    @Nullable
    public ChangeViewModel<Integer> eventStatusId;

    /**
     * The id for the location list def
     * location is typically 'community' / 'office' etc
     */
    @Nullable
    public ChangeViewModel<Integer> locationId;

    /** For Jackson etc */
    CommentCommandViewModel() {
        super();
    }

    public CommentCommandViewModel(UUID workUuid, int serviceRecipientId, EvidenceGroup evidenceGroup, EvidenceTask evidenceTask) {
        super(buildUri(serviceRecipientId, evidenceGroup, evidenceTask),
                serviceRecipientId);
        this.workUuid = workUuid;
    }

    public static String buildUri(int serviceRecipientId, EvidenceGroup evidenceGroup, EvidenceTask evidenceTask) {
        return UriComponentsBuilder
            .fromUriString("service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/{sourceTaskName}/comments/")
            .buildAndExpand(serviceRecipientId, evidenceGroup.nameAsLowercase(), evidenceTask.getTaskName())
            .toString();
    }

    public static FlagsDefinitionCommandDto toFlagsDefinitionCommand(String externalClientRef, AddedRemovedDto<Integer> flagIds, ListDefinitionRepository listDefinitionRepository) {
        if (flagIds == null) {
            return null;
        }

        var dto = new FlagsDefinitionCommandDto();
        dto.externalClientRef = externalClientRef;
        dto.flagNames = new AddedRemovedDto<>();

        dto.flagNames.from = flagIdsToBusinessKey(flagIds.from, listDefinitionRepository);
        dto.flagNames.added = flagIdsToBusinessKey(flagIds.added, listDefinitionRepository);
        dto.flagNames.removed = flagIdsToBusinessKey(flagIds.removed, listDefinitionRepository);

        return dto;
    }

    private static List<String> flagIdsToBusinessKey(List<Integer> flagIds, ListDefinitionRepository listDefinitionRepository) {
        return flagIds == null ? null
                : flagIds.stream()
                .map(f -> listDefinitionRepository.findById(f).orElseThrow())
                .map(ListDefinitionEntry::getBusinessKey)
                .toList();
    }
}
